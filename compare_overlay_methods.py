import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from utils.overlay_manager import OverlayManager
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def compare_overlay_methods(image_path, mask_path):
    """
    Compare les deux méthodes d'overlay sur une vraie image.
    """
    print(f"🔍 Comparaison des méthodes d'overlay...")
    print(f"Image: {image_path}")
    print(f"Masque: {mask_path}")
    
    # Vérifier que les fichiers existent
    if not os.path.exists(image_path):
        print(f"❌ Image non trouvée: {image_path}")
        return
    
    if not os.path.exists(mask_path):
        print(f"❌ Masque non trouvé: {mask_path}")
        return
    
    # Charger l'image et le masque
    original_img = cv2.imread(image_path)
    mask_img = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    
    if original_img is None:
        print(f"❌ Impossible de charger l'image")
        return
    
    if mask_img is None:
        print(f"❌ Impossible de charger le masque")
        return
    
    print(f"Image chargée: {original_img.shape}")
    print(f"Masque chargé: {mask_img.shape}")
    print(f"Valeurs uniques dans le masque: {np.unique(mask_img)}")
    
    # Créer les overlays avec les deux méthodes
    overlay_manager = OverlayManager()
    
    # Méthode standard
    overlay_standard = overlay_manager.create_overlay(original_img, mask_img, alpha=0.6)
    
    # Méthode haute visibilité
    overlay_high_contrast = overlay_manager.create_high_contrast_overlay(original_img, mask_img, alpha=0.8)
    
    # Créer une figure avec 4 sous-graphiques
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Image originale
    axes[0, 0].imshow(cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title('Image originale')
    axes[0, 0].axis('off')
    
    # Masque coloré
    color_mask = np.zeros((*mask_img.shape, 3))
    for class_value, color in overlay_manager.class_colors.items():
        mask_class = (mask_img == class_value)
        color_mask[mask_class] = color
    
    axes[0, 1].imshow(color_mask)
    axes[0, 1].set_title('Masque coloré')
    axes[0, 1].axis('off')
    
    # Overlay standard
    axes[1, 0].imshow(overlay_standard)
    axes[1, 0].set_title('Overlay standard (alpha=0.6)')
    axes[1, 0].axis('off')
    
    # Overlay haute visibilité
    axes[1, 1].imshow(overlay_high_contrast)
    axes[1, 1].set_title('Overlay haute visibilité (alpha=0.8)')
    axes[1, 1].axis('off')
    
    # Ajouter une légende des couleurs
    legend_text = "Légende des couleurs:\n"
    for class_value, color in overlay_manager.class_colors.items():
        class_name = overlay_manager.class_names[class_value]
        rgb_255 = [int(c * 255) for c in color]
        legend_text += f"• {class_name} (val={class_value}): RGB{rgb_255}\n"
    
    plt.figtext(0.02, 0.02, legend_text, fontsize=10, verticalalignment='bottom')
    
    plt.tight_layout()
    
    # Sauvegarder la comparaison
    output_path = 'comparison_overlay_methods.png'
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"✅ Comparaison sauvegardée: {output_path}")
    
    # Sauvegarder les overlays individuellement
    plt.figure(figsize=(10, 8))
    plt.imshow(overlay_high_contrast)
    plt.title('Overlay Haute Visibilité')
    plt.axis('off')
    plt.savefig('overlay_high_contrast.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    plt.figure(figsize=(10, 8))
    plt.imshow(overlay_standard)
    plt.title('Overlay Standard')
    plt.axis('off')
    plt.savefig('overlay_standard.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("✅ Overlays individuels sauvegardés: overlay_high_contrast.png, overlay_standard.png")

def find_test_files():
    """
    Trouve automatiquement des fichiers de test dans les dossiers de résultats.
    """
    base_paths = [
        r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference\inference_test4labeldifferent",
        r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldifferent"
    ]
    
    for base_path in base_paths:
        if os.path.exists(base_path):
            # Chercher une image
            for f in os.listdir(base_path):
                if f.endswith(('_0000.png', '.png', '.jpg', '.jpeg')):
                    image_path = os.path.join(base_path, f)
                    
                    # Chercher le masque correspondant
                    img_number = f.split('_')[0]
                    mask_name = f"{img_number}.png"
                    mask_path = os.path.join(base_path, "reconverted_masks", mask_name)
                    
                    if os.path.exists(mask_path):
                        return image_path, mask_path
    
    return None, None

if __name__ == "__main__":
    # Essayer de trouver automatiquement des fichiers de test
    image_path, mask_path = find_test_files()
    
    if image_path and mask_path:
        compare_overlay_methods(image_path, mask_path)
    else:
        print("❌ Aucun fichier de test trouvé automatiquement.")
        print("Modifiez les chemins dans le script ou assurez-vous que les fichiers existent.")
        
        # Chemins par défaut à modifier si nécessaire
        default_image = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldifferent\0001_0000.png"
        default_mask = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference\inference_test4labeldifferent\reconverted_masks\0001.png"
        
        print(f"Chemins par défaut:")
        print(f"Image: {default_image}")
        print(f"Masque: {default_mask}")
        
        if os.path.exists(default_image) and os.path.exists(default_mask):
            compare_overlay_methods(default_image, default_mask)
