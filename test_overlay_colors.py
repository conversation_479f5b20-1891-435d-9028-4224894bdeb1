import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from utils.overlay_manager import OverlayManager
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_overlay_colors():
    """
    Test les couleurs de l'overlay avec un masque synthétique.
    """
    print("🎨 Test des couleurs de l'overlay...")
    
    # Créer une image de test (512x512, grise)
    test_image = np.ones((512, 512, 3), dtype=np.uint8) * 128  # Gris moyen
    
    # Créer un masque de test avec toutes les classes
    test_mask = np.zeros((512, 512), dtype=np.uint8)
    
    # Diviser l'image en bandes pour chaque classe
    height = 512
    band_height = height // 5
    
    # Classe 0 (background) - première bande
    test_mask[0:band_height, :] = 0
    
    # Classe 29 (frontwall) - deuxième bande  
    test_mask[band_height:2*band_height, :] = 29
    
    # Classe 149 (backwall) - troisième bande
    test_mask[2*band_height:3*band_height, :] = 149
    
    # Classe 76 (flaw) - quatrième bande
    test_mask[3*band_height:4*band_height, :] = 76
    
    # Classe 125 (indication) - cinquième bande
    test_mask[4*band_height:, :] = 125
    
    print(f"Valeurs uniques dans le masque de test: {np.unique(test_mask)}")
    
    # Créer l'overlay
    overlay_manager = OverlayManager()
    overlay = overlay_manager.create_overlay(test_image, test_mask, alpha=0.6)
    
    # Créer une figure avec 3 sous-graphiques
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # Image originale
    axes[0].imshow(test_image)
    axes[0].set_title('Image originale')
    axes[0].axis('off')
    
    # Masque avec couleurs
    color_mask = np.zeros((*test_mask.shape, 3))
    for class_value, color in overlay_manager.class_colors.items():
        mask_class = (test_mask == class_value)
        color_mask[mask_class] = color
    
    axes[1].imshow(color_mask)
    axes[1].set_title('Masque coloré')
    axes[1].axis('off')
    
    # Overlay final
    axes[2].imshow(overlay)
    axes[2].set_title('Overlay final')
    axes[2].axis('off')
    
    # Ajouter une légende des couleurs
    legend_text = "Légende des couleurs:\n"
    for class_value, color in overlay_manager.class_colors.items():
        class_name = overlay_manager.class_names[class_value]
        rgb_255 = [int(c * 255) for c in color]
        legend_text += f"• {class_name} (val={class_value}): RGB{rgb_255}\n"
    
    plt.figtext(0.02, 0.02, legend_text, fontsize=10, verticalalignment='bottom')
    
    plt.tight_layout()
    plt.savefig('test_overlay_colors.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("✅ Test terminé. Image sauvegardée: test_overlay_colors.png")

def analyze_real_mask(mask_path):
    """
    Analyse un vrai masque pour voir quelles valeurs il contient.
    """
    if not os.path.exists(mask_path):
        print(f"❌ Fichier non trouvé: {mask_path}")
        return
    
    print(f"\n🔍 Analyse du masque: {mask_path}")
    
    # Charger le masque
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    if mask is None:
        print(f"❌ Impossible de charger le masque")
        return
    
    # Analyser les valeurs
    unique_vals = np.unique(mask)
    print(f"Valeurs uniques trouvées: {unique_vals}")
    
    # Compter les pixels pour chaque valeur
    overlay_manager = OverlayManager()
    for val in unique_vals:
        count = np.sum(mask == val)
        percentage = (count / mask.size) * 100
        class_name = overlay_manager.class_names.get(val, f"inconnue_{val}")
        has_color = val in overlay_manager.class_colors
        status = "✅" if has_color else "❌"
        print(f"{status} Valeur {val} ({class_name}): {count} pixels ({percentage:.2f}%)")

if __name__ == "__main__":
    # Test avec masque synthétique
    test_overlay_colors()
    
    # Analyser un vrai masque si disponible
    # Remplacez ce chemin par un de vos vrais masques
    real_mask_path = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference\inference_test4labeldifferent\reconverted_masks\0001.png"
    
    if os.path.exists(real_mask_path):
        analyze_real_mask(real_mask_path)
    else:
        print(f"\n⚠️ Masque réel non trouvé: {real_mask_path}")
        print("Modifiez le chemin dans le script pour analyser vos vrais masques.")
