import os
import numpy as np
import cv2
import logging
from typing import Tuple, Optional
import matplotlib.pyplot as plt
from skimage.transform import resize

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OverlayManager:
    """
    Gère la superposition des masques sur les images.
    """
    
    def __init__(self):
        """Initialise le gestionnaire d'overlay."""
        self._logger = logger
        # Définition des couleurs pour chaque classe
        self.class_colors = {
            0: [0, 0, 0],      # background (noir)
            29: [0, 0, 1],     # frontwall (bleu)
            149: [1, 0, 0],    # backwall (rouge)
            76: [0, 1, 0],     # flaw (vert)
            125: [1, 1, 0]     # indication (jaune)
        }

    def create_overlay(
        self,
        original_image: np.ndarray,
        mask_image: np.ndarray,
        alpha: float = 0.4
    ) -> np.ndarray:
        """
        Crée une superposition de l'image originale et du masque.
        
        Args:
            original_image: Image originale en format BGR
            mask_image: Image du masque (binaire ou RGB)
            alpha: Transparence du masque (0-1)
            
        Returns:
            numpy.ndarray: Image avec overlay
        """
        try:
            self._logger.info(f"Création de l'overlay avec alpha={alpha}")
            self._logger.info(f"Type de l'image originale: {original_image.dtype}, forme: {original_image.shape}")
            self._logger.info(f"Type du masque: {mask_image.dtype}, forme: {mask_image.shape}")

            # Convertir l'image originale en RGB si nécessaire
            if original_image.ndim == 2:
                original_image = np.stack([original_image] * 3, axis=-1)
            elif original_image.shape[2] == 3:
                original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)

            # Convertir le masque en RGB si nécessaire
            if mask_image.ndim == 2:
                unique_vals = np.unique(mask_image)
                self._logger.info(f"Valeurs uniques dans le masque: {unique_vals}")
                
                # Créer un masque coloré
                color_mask = np.zeros((*mask_image.shape, 3))
                
                # Appliquer les couleurs pour chaque classe
                for class_value, color in self.class_colors.items():
                    mask_class = (mask_image == class_value)
                    color_mask[mask_class] = color
                    self._logger.info(f"Classe {class_value}: {np.sum(mask_class)} pixels")
                
                mask_image = color_mask

            # Redimensionner le masque si nécessaire
            if mask_image.shape[:2] != original_image.shape[:2]:
                self._logger.warning("Redimensionnement du masque pour correspondre à l'image originale")
                mask_image = resize(mask_image, original_image.shape[:2], preserve_range=True, anti_aliasing=False)

            # Normalisation
            if original_image.dtype == np.uint8:
                original_image = original_image.astype(float) / 255.0
            if mask_image.dtype == np.uint8:
                mask_image = mask_image.astype(float) / 255.0

            # Créer l'overlay avec une visibilité accrue du masque
            mask_alpha = (mask_image > 0).any(axis=-1, keepdims=True)
            
            # Assombrir légèrement l'image originale sous le masque
            darkened = original_image * 0.7
            
            # Combiner l'image assombrie et le masque coloré
            overlay = np.where(mask_alpha, 
                             darkened * (1 - alpha) + mask_image * alpha,
                             original_image)
            
            overlay = np.clip(overlay, 0, 1)
            
            self._logger.info("Overlay créé avec succès")
            return overlay

        except Exception as e:
            self._logger.error(f"Erreur lors de la création de l'overlay: {str(e)}")
            raise

    def save_overlay(
        self,
        overlay: np.ndarray,
        output_path: str
    ) -> None:
        """
        Sauvegarde l'overlay dans un fichier.
        
        Args:
            overlay: Image avec overlay
            output_path: Chemin de sauvegarde
        """
        try:
            # Créer le dossier de sortie si nécessaire
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Sauvegarder l'image avec matplotlib
            plt.imsave(output_path, overlay)
            self._logger.info(f"Overlay sauvegardé: {output_path}")
            
        except Exception as e:
            self._logger.error(f"Erreur lors de la sauvegarde de l'overlay: {str(e)}")
            raise 