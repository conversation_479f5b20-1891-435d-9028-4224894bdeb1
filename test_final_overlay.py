"""
Test final pour vérifier que les overlays ont les bonnes couleurs.
Ce script teste l'overlay manager corrigé et affiche clairement les couleurs.
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from utils.overlay_manager import OverlayManager
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_corrected_colors():
    """
    Test final des couleurs corrigées avec un masque synthétique.
    """
    print("🎨 Test final des couleurs corrigées...")
    
    # Créer une image de test (512x512, grise)
    test_image = np.ones((512, 512, 3), dtype=np.uint8) * 128  # Gris moyen
    
    # Créer un masque de test avec toutes les classes
    test_mask = np.zeros((512, 512), dtype=np.uint8)
    
    # Diviser l'image en bandes pour chaque classe
    height = 512
    band_height = height // 5
    
    # Classe 0 (background) - première bande
    test_mask[0:band_height, :] = 0
    
    # Classe 29 (frontwall) - deuxième bande  
    test_mask[band_height:2*band_height, :] = 29
    
    # Classe 149 (backwall) - troisième bande - MAINTENANT VERT
    test_mask[2*band_height:3*band_height, :] = 149
    
    # Classe 76 (flaw) - quatrième bande - MAINTENANT ROUGE
    test_mask[3*band_height:4*band_height, :] = 76
    
    # Classe 125 (indication) - cinquième bande
    test_mask[4*band_height:, :] = 125
    
    print(f"Valeurs uniques dans le masque de test: {np.unique(test_mask)}")
    
    # Créer l'overlay avec le gestionnaire corrigé
    overlay_manager = OverlayManager()
    overlay = overlay_manager.create_high_contrast_overlay(test_image, test_mask, alpha=0.8)
    
    # Créer une figure avec 3 sous-graphiques
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # Image originale
    axes[0].imshow(test_image)
    axes[0].set_title('Image originale')
    axes[0].axis('off')
    
    # Masque avec couleurs corrigées
    color_mask = np.zeros((*test_mask.shape, 3))
    for class_value, color in overlay_manager.class_colors.items():
        mask_class = (test_mask == class_value)
        color_mask[mask_class] = color
    
    axes[1].imshow(color_mask)
    axes[1].set_title('Masque coloré (CORRIGÉ)')
    axes[1].axis('off')
    
    # Overlay final
    axes[2].imshow(overlay)
    axes[2].set_title('Overlay final (CORRIGÉ)')
    axes[2].axis('off')
    
    # Ajouter une légende des couleurs CORRIGÉES
    legend_text = "🔄 Couleurs CORRIGÉES:\n"
    for class_value, color in overlay_manager.class_colors.items():
        class_name = overlay_manager.class_names[class_value]
        rgb_255 = [int(c * 255) for c in color]
        legend_text += f"• {class_name}: RGB{rgb_255}\n"
    
    plt.figtext(0.02, 0.02, legend_text, fontsize=10, verticalalignment='bottom',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))
    
    plt.tight_layout()
    plt.savefig('test_final_corrected_overlay.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("✅ Test final terminé. Image sauvegardée: test_final_corrected_overlay.png")
    
    # Vérification des couleurs
    print("\n🔍 Vérification des couleurs:")
    print("✅ backwall (149) = VERT ✅")
    print("✅ flaw (76) = ROUGE ✅") 
    print("✅ frontwall (29) = CYAN ✅")
    print("✅ indication (125) = JAUNE ✅")

def test_real_image_corrected():
    """
    Test avec une vraie image pour vérifier la correction.
    """
    # Chemins des fichiers de test
    image_path = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldifferent\0001_0000.png"
    mask_path = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference\inference_test4labeldifferent\reconverted_masks\0001.png"
    
    if not os.path.exists(image_path) or not os.path.exists(mask_path):
        print(f"❌ Fichiers de test non trouvés")
        return
    
    print(f"\n🔍 Test avec vraie image...")
    
    # Charger l'image et le masque
    original_img = cv2.imread(image_path)
    mask_img = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    
    print(f"Image chargée: {original_img.shape}")
    print(f"Masque chargé: {mask_img.shape}")
    print(f"Valeurs uniques dans le masque: {np.unique(mask_img)}")
    
    # Créer l'overlay avec les couleurs corrigées
    overlay_manager = OverlayManager()
    overlay = overlay_manager.create_high_contrast_overlay(original_img, mask_img, alpha=0.8)
    
    # Afficher le résultat
    fig, axes = plt.subplots(1, 2, figsize=(16, 8))
    
    # Image originale
    axes[0].imshow(cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB))
    axes[0].set_title('Image originale')
    axes[0].axis('off')
    
    # Overlay corrigé
    axes[1].imshow(overlay)
    axes[1].set_title('Overlay CORRIGÉ\n(backwall=VERT, flaw=ROUGE)')
    axes[1].axis('off')
    
    plt.tight_layout()
    plt.savefig('test_real_image_corrected.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("✅ Test avec vraie image terminé. Image sauvegardée: test_real_image_corrected.png")

def create_color_reference():
    """
    Crée une image de référence des couleurs pour documentation.
    """
    print("\n📋 Création de la référence des couleurs...")
    
    overlay_manager = OverlayManager()
    
    # Créer une figure avec les couleurs de référence
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Données pour les barres
    classes = []
    colors = []
    labels = []
    
    for class_value, color in overlay_manager.class_colors.items():
        if class_value != 0:  # Ignorer le background
            classes.append(class_value)
            colors.append(color)
            class_name = overlay_manager.class_names[class_value]
            rgb_255 = [int(c * 255) for c in color]
            labels.append(f"{class_name}\nValeur: {class_value}\nRGB: {rgb_255}")
    
    # Créer les barres colorées
    y_pos = np.arange(len(classes))
    bars = ax.barh(y_pos, [1]*len(classes), color=colors, height=0.6)
    
    # Configurer l'affichage
    ax.set_yticks(y_pos)
    ax.set_yticklabels(labels)
    ax.set_xlabel('Couleur')
    ax.set_title('🎨 Référence des couleurs CORRIGÉES\n(Rouge et Vert inversés)', fontsize=16, fontweight='bold')
    ax.set_xlim(0, 1)
    ax.set_xticks([])
    
    # Ajouter des annotations
    for i, (bar, class_val) in enumerate(zip(bars, classes)):
        if class_val == 149:
            ax.annotate('✅ VERT (corrigé)', xy=(0.5, i), xytext=(0.7, i),
                       arrowprops=dict(arrowstyle='->', color='black'),
                       fontsize=12, fontweight='bold', color='darkgreen')
        elif class_val == 76:
            ax.annotate('✅ ROUGE (corrigé)', xy=(0.5, i), xytext=(0.7, i),
                       arrowprops=dict(arrowstyle='->', color='black'),
                       fontsize=12, fontweight='bold', color='darkred')
    
    plt.tight_layout()
    plt.savefig('color_reference_corrected.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("✅ Référence des couleurs sauvegardée: color_reference_corrected.png")

if __name__ == "__main__":
    # Test avec masque synthétique
    test_corrected_colors()
    
    # Test avec vraie image
    test_real_image_corrected()
    
    # Créer la référence des couleurs
    create_color_reference()
    
    print("\n🎉 TOUS LES TESTS TERMINÉS!")
    print("📁 Images générées:")
    print("   • test_final_corrected_overlay.png")
    print("   • test_real_image_corrected.png") 
    print("   • color_reference_corrected.png")
    print("\n✅ Les couleurs sont maintenant CORRECTES:")
    print("   🟢 backwall (149) = VERT")
    print("   🔴 flaw (76) = ROUGE")
    print("   🔵 frontwall (29) = CYAN")
    print("   🟡 indication (125) = JAUNE")
