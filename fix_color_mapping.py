import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from utils.overlay_manager import OverlayManager
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_corrected_overlay_manager():
    """
    Crée un gestionnaire d'overlay avec un mapping de couleurs corrigé.
    """
    class CorrectedOverlayManager(OverlayManager):
        def __init__(self):
            super().__init__()
            # Mapping de couleurs corrigé si nécessaire
            # Vous pouvez modifier ces couleurs selon vos préférences
            self.class_colors = {
                0: [0, 0, 0],        # background (noir)
                29: [0, 0.7, 1],     # frontwall (bleu cyan)
                149: [1, 0, 0],      # backwall (rouge vif)
                76: [0, 1, 0],       # flaw (vert vif) 
                125: [1, 1, 0]       # indication (jaune vif)
            }
            
            # Si vous voulez inverser rouge et vert, décommentez ces lignes :
            # self.class_colors[149] = [0, 1, 0]  # backwall en vert
            # self.class_colors[76] = [1, 0, 0]   # flaw en rouge
            
            self.class_names = {
                0: "background",
                29: "frontwall", 
                149: "backwall",
                76: "flaw",
                125: "indication"
            }
    
    return CorrectedOverlayManager()

def test_color_combinations(image_path, mask_path):
    """
    Teste différentes combinaisons de couleurs pour voir laquelle est la plus appropriée.
    """
    print(f"🎨 Test de différentes combinaisons de couleurs...")
    
    # Charger l'image et le masque
    original_img = cv2.imread(image_path)
    mask_img = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    
    if original_img is None or mask_img is None:
        print(f"❌ Impossible de charger les fichiers")
        return
    
    # Définir différentes combinaisons de couleurs
    color_schemes = {
        "Standard": {
            0: [0, 0, 0],        # background (noir)
            29: [0, 0.7, 1],     # frontwall (bleu)
            149: [1, 0, 0],      # backwall (rouge)
            76: [0, 1, 0],       # flaw (vert)
            125: [1, 1, 0]       # indication (jaune)
        },
        "Inversé Rouge/Vert": {
            0: [0, 0, 0],        # background (noir)
            29: [0, 0.7, 1],     # frontwall (bleu)
            149: [0, 1, 0],      # backwall (vert)
            76: [1, 0, 0],       # flaw (rouge)
            125: [1, 1, 0]       # indication (jaune)
        },
        "Couleurs Vives": {
            0: [0, 0, 0],        # background (noir)
            29: [0, 1, 1],       # frontwall (cyan)
            149: [1, 0, 1],      # backwall (magenta)
            76: [1, 0.5, 0],     # flaw (orange)
            125: [1, 1, 0]       # indication (jaune)
        },
        "Couleurs Pastel": {
            0: [0, 0, 0],        # background (noir)
            29: [0.5, 0.8, 1],   # frontwall (bleu clair)
            149: [1, 0.5, 0.5],  # backwall (rose)
            76: [0.5, 1, 0.5],   # flaw (vert clair)
            125: [1, 1, 0.5]     # indication (jaune clair)
        }
    }
    
    # Créer une figure avec tous les schémas de couleurs
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    # Image originale
    axes[0].imshow(cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB))
    axes[0].set_title('Image originale')
    axes[0].axis('off')
    
    # Tester chaque schéma de couleurs
    for i, (scheme_name, colors) in enumerate(color_schemes.items(), 1):
        # Créer un masque coloré avec ce schéma
        color_mask = np.zeros((*mask_img.shape, 3))
        for class_value, color in colors.items():
            mask_class = (mask_img == class_value)
            color_mask[mask_class] = color
        
        # Convertir BGR vers RGB et normaliser
        orig_rgb = cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB).astype(float) / 255.0

        # Créer l'overlay
        mask_alpha = (color_mask > 0).any(axis=-1, keepdims=True)
        overlay = orig_rgb.copy()
        overlay[mask_alpha.squeeze()] = (overlay[mask_alpha.squeeze()] * 0.4 +
                                       color_mask[mask_alpha.squeeze()] * 0.6)
        overlay = np.clip(overlay, 0, 1)
        
        axes[i].imshow(overlay)
        axes[i].set_title(f'{scheme_name}')
        axes[i].axis('off')
    
    # Masquer le dernier subplot s'il n'est pas utilisé
    if len(color_schemes) < 5:
        axes[5].axis('off')
    
    plt.tight_layout()
    plt.savefig('color_schemes_comparison.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("✅ Comparaison des schémas de couleurs sauvegardée: color_schemes_comparison.png")
    
    # Afficher la légende pour chaque schéma
    print("\n📋 Légendes des couleurs:")
    for scheme_name, colors in color_schemes.items():
        print(f"\n{scheme_name}:")
        for class_value, color in colors.items():
            if class_value in [29, 149, 76, 125]:  # Ignorer le background
                class_names = {29: "frontwall", 149: "backwall", 76: "flaw", 125: "indication"}
                rgb_255 = [int(c * 255) for c in color]
                print(f"  • {class_names[class_value]} (val={class_value}): RGB{rgb_255}")

def create_custom_overlay(image_path, mask_path, custom_colors=None):
    """
    Crée un overlay avec des couleurs personnalisées.
    """
    if custom_colors is None:
        # Couleurs par défaut recommandées
        custom_colors = {
            0: [0, 0, 0],        # background (noir)
            29: [0, 0.8, 1],     # frontwall (cyan vif)
            149: [1, 0.2, 0.2],  # backwall (rouge vif)
            76: [0.2, 1, 0.2],   # flaw (vert vif)
            125: [1, 1, 0.2]     # indication (jaune vif)
        }
    
    # Charger les fichiers
    original_img = cv2.imread(image_path)
    mask_img = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    
    if original_img is None or mask_img is None:
        print(f"❌ Impossible de charger les fichiers")
        return None
    
    # Créer le masque coloré
    color_mask = np.zeros((*mask_img.shape, 3))
    for class_value, color in custom_colors.items():
        mask_class = (mask_img == class_value)
        color_mask[mask_class] = color
    
    # Convertir BGR vers RGB et normaliser
    orig_rgb = cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB).astype(float) / 255.0

    # Créer l'overlay avec méthode haute visibilité
    mask_alpha = (color_mask > 0).any(axis=-1, keepdims=True)

    # Convertir l'arrière-plan en niveaux de gris et l'assombrir
    gray_bg = np.mean(orig_rgb, axis=-1, keepdims=True)
    gray_bg = np.repeat(gray_bg, 3, axis=-1) * 0.3

    # Créer l'overlay
    overlay = np.where(mask_alpha, gray_bg, orig_rgb)
    overlay = overlay + color_mask * 0.7
    overlay = np.clip(overlay, 0, 1)
    
    return overlay

if __name__ == "__main__":
    # Chemins des fichiers de test
    image_path = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldifferent\0001_0000.png"
    mask_path = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference\inference_test4labeldifferent\reconverted_masks\0001.png"
    
    if os.path.exists(image_path) and os.path.exists(mask_path):
        # Tester différents schémas de couleurs
        test_color_combinations(image_path, mask_path)
        
        # Créer un overlay personnalisé optimisé
        print("\n🎨 Création d'un overlay optimisé...")
        optimized_overlay = create_custom_overlay(image_path, mask_path)
        
        if optimized_overlay is not None:
            plt.figure(figsize=(12, 8))
            plt.imshow(optimized_overlay)
            plt.title('Overlay Optimisé - Couleurs Haute Visibilité')
            plt.axis('off')
            plt.savefig('optimized_overlay.png', dpi=150, bbox_inches='tight')
            plt.show()
            print("✅ Overlay optimisé sauvegardé: optimized_overlay.png")
    else:
        print(f"❌ Fichiers non trouvés:")
        print(f"Image: {image_path}")
        print(f"Masque: {mask_path}")
