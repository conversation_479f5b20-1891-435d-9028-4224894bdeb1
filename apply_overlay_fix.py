"""
Script pour appliquer les corrections d'overlay avec des couleurs améliorées.
Ce script recrée tous les overlays avec une meilleure visibilité des couleurs.
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from utils.overlay_manager import OverlayManager
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedOverlayManager(OverlayManager):
    """
    Version améliorée du gestionnaire d'overlay avec des couleurs plus visibles.
    """
    
    def __init__(self, color_scheme="optimized"):
        super().__init__()
        
        # Différents schémas de couleurs disponibles
        color_schemes = {
            "standard": {
                0: [0, 0, 0],        # background (noir)
                29: [0, 0.7, 1],     # frontwall (bleu)
                149: [1, 0, 0],      # backwall (rouge)
                76: [0, 1, 0],       # flaw (vert)
                125: [1, 1, 0]       # indication (jaune)
            },
            "high_contrast": {
                0: [0, 0, 0],        # background (noir)
                29: [0, 0.8, 1],     # frontwall (cyan vif)
                149: [1, 0.2, 0.2],  # backwall (rouge vif)
                76: [0.2, 1, 0.2],   # flaw (vert vif)
                125: [1, 1, 0.2]     # indication (jaune vif)
            },
            "optimized": {
                0: [0, 0, 0],        # background (noir)
                29: [0, 0.9, 1],     # frontwall (cyan brillant)
                149: [1, 0.1, 0.1],  # backwall (rouge pur)
                76: [0.1, 1, 0.1],   # flaw (vert pur)
                125: [1, 1, 0.1]     # indication (jaune pur)
            },
            "inverted": {
                0: [0, 0, 0],        # background (noir)
                29: [0, 0.7, 1],     # frontwall (bleu)
                149: [0, 1, 0],      # backwall (vert) - INVERSÉ
                76: [1, 0, 0],       # flaw (rouge) - INVERSÉ
                125: [1, 1, 0]       # indication (jaune)
            }
        }
        
        if color_scheme in color_schemes:
            self.class_colors = color_schemes[color_scheme]
            logger.info(f"Schéma de couleurs '{color_scheme}' appliqué")
        else:
            logger.warning(f"Schéma '{color_scheme}' non trouvé, utilisation du schéma standard")
            self.class_colors = color_schemes["standard"]
    
    def create_enhanced_overlay(
        self,
        original_image: np.ndarray,
        mask_image: np.ndarray,
        alpha: float = 0.8,
        background_darken: float = 0.3
    ) -> np.ndarray:
        """
        Crée un overlay avec une visibilité maximale des couleurs.
        """
        try:
            self._logger.info(f"Création de l'overlay amélioré avec alpha={alpha}")
            
            # Convertir l'image originale en RGB si nécessaire
            if original_image.ndim == 2:
                original_image = np.stack([original_image] * 3, axis=-1)
            elif original_image.shape[2] == 3:
                original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)

            # Convertir le masque en RGB si nécessaire
            if mask_image.ndim == 2:
                unique_vals = np.unique(mask_image)
                self._logger.info(f"Valeurs uniques dans le masque: {unique_vals}")
                
                # Créer un masque coloré
                color_mask = np.zeros((*mask_image.shape, 3))
                
                # Appliquer les couleurs pour chaque classe
                for class_value, color in self.class_colors.items():
                    mask_class = (mask_image == class_value)
                    pixel_count = np.sum(mask_class)
                    if pixel_count > 0:
                        color_mask[mask_class] = color
                        class_name = self.class_names.get(class_value, f"classe_{class_value}")
                        self._logger.info(f"✅ {class_name} (valeur {class_value}): {pixel_count} pixels")
                
                mask_image = color_mask

            # Normalisation
            if original_image.dtype == np.uint8:
                original_image = original_image.astype(float) / 255.0
            if mask_image.dtype == np.uint8:
                mask_image = mask_image.astype(float) / 255.0

            # Créer l'overlay avec méthode optimisée
            mask_alpha = (mask_image > 0).any(axis=-1, keepdims=True)
            
            # Convertir l'arrière-plan en niveaux de gris et l'assombrir
            gray_bg = np.mean(original_image, axis=-1, keepdims=True)
            gray_bg = np.repeat(gray_bg, 3, axis=-1) * background_darken
            
            # Créer l'overlay
            overlay = np.where(mask_alpha, gray_bg, original_image)
            
            # Ajouter les couleurs du masque de manière additive
            overlay = overlay + mask_image * alpha
            
            overlay = np.clip(overlay, 0, 1)
            
            self._logger.info("Overlay amélioré créé avec succès")
            return overlay

        except Exception as e:
            self._logger.error(f"Erreur lors de la création de l'overlay amélioré: {str(e)}")
            raise

def recreate_overlays_with_improved_colors(
    input_folder: str,
    mask_folder: str,
    output_folder: str,
    color_scheme: str = "optimized"
):
    """
    Recrée tous les overlays avec des couleurs améliorées.
    """
    print(f"\n🎨 Recréation des overlays avec le schéma '{color_scheme}'...")
    
    # Créer le dossier de sortie
    os.makedirs(output_folder, exist_ok=True)
    
    # Créer le gestionnaire d'overlay amélioré
    overlay_manager = ImprovedOverlayManager(color_scheme=color_scheme)
    
    # Traiter chaque image
    processed_count = 0
    for img_name in os.listdir(input_folder):
        if img_name.endswith(('.png', '.jpg', '.jpeg')):
            try:
                # Charger l'image originale
                img_path = os.path.join(input_folder, img_name)
                logger.info(f"Traitement de l'image: {img_name}")
                
                original_img = cv2.imread(img_path)
                if original_img is None:
                    logger.error(f"Impossible de charger l'image: {img_path}")
                    continue
                    
                # Charger le masque correspondant
                img_number = img_name.split('_')[0]
                mask_name = f"{img_number}.png"
                mask_path = os.path.join(mask_folder, mask_name)
                
                if not os.path.exists(mask_path):
                    logger.error(f"Masque non trouvé: {mask_path}")
                    continue
                    
                mask_img = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
                if mask_img is None:
                    logger.error(f"Impossible de charger le masque: {mask_path}")
                    continue
                
                # Créer l'overlay amélioré
                overlay = overlay_manager.create_enhanced_overlay(original_img, mask_img)
                
                # Sauvegarder l'overlay
                output_path = os.path.join(output_folder, f"enhanced_overlay_{img_name}")
                overlay_manager.save_overlay(overlay, output_path)
                logger.info(f"Overlay amélioré sauvegardé: {output_path}")
                
                processed_count += 1
                
            except Exception as e:
                logger.error(f"Erreur lors du traitement de {img_name}: {str(e)}")
                continue
    
    print(f"✅ {processed_count} overlays améliorés créés dans: {output_folder}")

def create_color_legend(color_scheme: str = "optimized"):
    """
    Crée une légende des couleurs utilisées.
    """
    overlay_manager = ImprovedOverlayManager(color_scheme=color_scheme)
    
    # Créer une image de légende
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # Créer des rectangles colorés pour chaque classe
    y_positions = []
    colors = []
    labels = []
    
    for i, (class_value, color) in enumerate(overlay_manager.class_colors.items()):
        if class_value != 0:  # Ignorer le background
            y_positions.append(i)
            colors.append(color)
            class_name = overlay_manager.class_names[class_value]
            labels.append(f"{class_name} (valeur {class_value})")
    
    # Créer les barres colorées
    bars = ax.barh(y_positions, [1]*len(colors), color=colors)
    
    # Configurer l'affichage
    ax.set_yticks(y_positions)
    ax.set_yticklabels(labels)
    ax.set_xlabel('Couleur')
    ax.set_title(f'Légende des couleurs - Schéma "{color_scheme}"')
    ax.set_xlim(0, 1)
    
    # Supprimer les ticks de l'axe x
    ax.set_xticks([])
    
    plt.tight_layout()
    plt.savefig(f'color_legend_{color_scheme}.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"✅ Légende des couleurs sauvegardée: color_legend_{color_scheme}.png")

if __name__ == "__main__":
    # Configuration des dossiers
    INPUT_FOLDER = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldifferent"
    MASK_FOLDER = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference\inference_test4labeldifferent\reconverted_masks"
    OUTPUT_FOLDER = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference\inference_test4labeldifferent\enhanced_overlays"
    
    # Vérifier que les dossiers existent
    if not os.path.exists(INPUT_FOLDER):
        print(f"❌ Dossier d'entrée non trouvé: {INPUT_FOLDER}")
    elif not os.path.exists(MASK_FOLDER):
        print(f"❌ Dossier des masques non trouvé: {MASK_FOLDER}")
    else:
        # Créer la légende des couleurs
        create_color_legend("optimized")
        
        # Recréer les overlays avec des couleurs améliorées
        recreate_overlays_with_improved_colors(
            INPUT_FOLDER,
            MASK_FOLDER,
            OUTPUT_FOLDER,
            color_scheme="optimized"
        )
        
        print(f"\n🎯 Overlays améliorés créés avec succès!")
        print(f"📁 Dossier de sortie: {OUTPUT_FOLDER}")
        print(f"🎨 Schéma de couleurs utilisé: optimized")
        print(f"📋 Légende sauvegardée: color_legend_optimized.png")
