"""
Script pour corriger l'inversion rouge/vert dans les overlays.
Ce script crée des overlays avec les couleurs rouge et vert inversées.
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from utils.overlay_manager import OverlayManager
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CorrectedOverlayManager(OverlayManager):
    """
    Gestionnaire d'overlay avec les couleurs rouge et vert inversées.
    """
    
    def __init__(self):
        super().__init__()
        
        # Couleurs CORRIGÉES avec rouge et vert inversés
        self.class_colors = {
            0: [0, 0, 0],        # background (noir)
            29: [0, 0.9, 1],     # frontwall (cyan brillant)
            149: [0.1, 1, 0.1],  # backwall (VERT) - INVERSÉ
            76: [1, 0.1, 0.1],   # flaw (ROUGE) - INVERSÉ  
            125: [1, 1, 0.1]     # indication (jaune pur)
        }
        
        # Noms des classes pour le debug
        self.class_names = {
            0: "background",
            29: "frontwall", 
            149: "backwall (maintenant VERT)",
            76: "flaw (maintenant ROUGE)",
            125: "indication"
        }
        
        logger.info("🔄 Couleurs rouge et vert inversées:")
        logger.info("   • backwall (149) = VERT")
        logger.info("   • flaw (76) = ROUGE")
    
    def create_corrected_overlay(
        self,
        original_image: np.ndarray,
        mask_image: np.ndarray,
        alpha: float = 0.8,
        background_darken: float = 0.3
    ) -> np.ndarray:
        """
        Crée un overlay avec les couleurs rouge et vert inversées.
        """
        try:
            self._logger.info(f"Création de l'overlay corrigé avec alpha={alpha}")
            
            # Convertir l'image originale en RGB si nécessaire
            if original_image.ndim == 2:
                original_image = np.stack([original_image] * 3, axis=-1)
            elif original_image.shape[2] == 3:
                original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)

            # Convertir le masque en RGB si nécessaire
            if mask_image.ndim == 2:
                unique_vals = np.unique(mask_image)
                self._logger.info(f"Valeurs uniques dans le masque: {unique_vals}")
                
                # Créer un masque coloré avec les couleurs corrigées
                color_mask = np.zeros((*mask_image.shape, 3))
                
                # Appliquer les couleurs CORRIGÉES pour chaque classe
                for class_value, color in self.class_colors.items():
                    mask_class = (mask_image == class_value)
                    pixel_count = np.sum(mask_class)
                    if pixel_count > 0:
                        color_mask[mask_class] = color
                        class_name = self.class_names.get(class_value, f"classe_{class_value}")
                        rgb_255 = [int(c * 255) for c in color]
                        self._logger.info(f"✅ {class_name}: {pixel_count} pixels - RGB{rgb_255}")
                
                mask_image = color_mask

            # Normalisation
            if original_image.dtype == np.uint8:
                original_image = original_image.astype(float) / 255.0
            if mask_image.dtype == np.uint8:
                mask_image = mask_image.astype(float) / 255.0

            # Créer l'overlay avec méthode optimisée
            mask_alpha = (mask_image > 0).any(axis=-1, keepdims=True)
            
            # Convertir l'arrière-plan en niveaux de gris et l'assombrir
            gray_bg = np.mean(original_image, axis=-1, keepdims=True)
            gray_bg = np.repeat(gray_bg, 3, axis=-1) * background_darken
            
            # Créer l'overlay
            overlay = np.where(mask_alpha, gray_bg, original_image)
            
            # Ajouter les couleurs du masque de manière additive
            overlay = overlay + mask_image * alpha
            
            overlay = np.clip(overlay, 0, 1)
            
            self._logger.info("Overlay corrigé créé avec succès")
            return overlay

        except Exception as e:
            self._logger.error(f"Erreur lors de la création de l'overlay corrigé: {str(e)}")
            raise

def create_corrected_overlays(
    input_folder: str,
    mask_folder: str,
    output_folder: str
):
    """
    Crée tous les overlays avec les couleurs rouge et vert inversées.
    """
    print(f"\n🔄 Création des overlays avec rouge et vert inversés...")
    
    # Créer le dossier de sortie
    os.makedirs(output_folder, exist_ok=True)
    
    # Créer le gestionnaire d'overlay corrigé
    overlay_manager = CorrectedOverlayManager()
    
    # Traiter chaque image
    processed_count = 0
    for img_name in os.listdir(input_folder):
        if img_name.endswith(('.png', '.jpg', '.jpeg')):
            try:
                # Charger l'image originale
                img_path = os.path.join(input_folder, img_name)
                logger.info(f"Traitement de l'image: {img_name}")
                
                original_img = cv2.imread(img_path)
                if original_img is None:
                    logger.error(f"Impossible de charger l'image: {img_path}")
                    continue
                    
                # Charger le masque correspondant
                img_number = img_name.split('_')[0]
                mask_name = f"{img_number}.png"
                mask_path = os.path.join(mask_folder, mask_name)
                
                if not os.path.exists(mask_path):
                    logger.error(f"Masque non trouvé: {mask_path}")
                    continue
                    
                mask_img = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
                if mask_img is None:
                    logger.error(f"Impossible de charger le masque: {mask_path}")
                    continue
                
                # Créer l'overlay corrigé
                overlay = overlay_manager.create_corrected_overlay(original_img, mask_img)
                
                # Sauvegarder l'overlay
                output_path = os.path.join(output_folder, f"corrected_overlay_{img_name}")
                overlay_manager.save_overlay(overlay, output_path)
                logger.info(f"Overlay corrigé sauvegardé: {output_path}")
                
                processed_count += 1
                
            except Exception as e:
                logger.error(f"Erreur lors du traitement de {img_name}: {str(e)}")
                continue
    
    print(f"✅ {processed_count} overlays corrigés créés dans: {output_folder}")

def create_comparison_before_after(image_path, mask_path):
    """
    Crée une comparaison avant/après pour vérifier la correction.
    """
    print(f"\n🔍 Création d'une comparaison avant/après...")
    
    # Charger l'image et le masque
    original_img = cv2.imread(image_path)
    mask_img = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    
    if original_img is None or mask_img is None:
        print(f"❌ Impossible de charger les fichiers")
        return
    
    # Créer l'overlay AVANT correction (couleurs originales)
    from apply_overlay_fix import ImprovedOverlayManager
    original_manager = ImprovedOverlayManager(color_scheme="optimized")
    overlay_before = original_manager.create_enhanced_overlay(original_img, mask_img, alpha=0.8)
    
    # Créer l'overlay APRÈS correction (rouge et vert inversés)
    corrected_manager = CorrectedOverlayManager()
    overlay_after = corrected_manager.create_corrected_overlay(original_img, mask_img, alpha=0.8)
    
    # Créer la comparaison
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # Image originale
    axes[0].imshow(cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB))
    axes[0].set_title('Image originale')
    axes[0].axis('off')
    
    # Overlay AVANT (rouge et vert inversés)
    axes[1].imshow(overlay_before)
    axes[1].set_title('AVANT correction\n(backwall=rouge, flaw=vert)')
    axes[1].axis('off')
    
    # Overlay APRÈS (rouge et vert corrigés)
    axes[2].imshow(overlay_after)
    axes[2].set_title('APRÈS correction\n(backwall=vert, flaw=rouge)')
    axes[2].axis('off')
    
    # Ajouter une légende
    legend_text = "Correction rouge/vert:\n"
    legend_text += "• backwall (149): rouge → VERT\n"
    legend_text += "• flaw (76): vert → ROUGE\n"
    legend_text += "• frontwall (29): cyan (inchangé)\n"
    legend_text += "• indication (125): jaune (inchangé)"
    
    plt.figtext(0.02, 0.02, legend_text, fontsize=12, verticalalignment='bottom',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
    
    plt.tight_layout()
    plt.savefig('red_green_correction_comparison.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("✅ Comparaison sauvegardée: red_green_correction_comparison.png")

if __name__ == "__main__":
    # Configuration des dossiers
    INPUT_FOLDER = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldifferent"
    MASK_FOLDER = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference\inference_test4labeldifferent\reconverted_masks"
    OUTPUT_FOLDER = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference\inference_test4labeldifferent\corrected_overlays"
    
    # Vérifier que les dossiers existent
    if not os.path.exists(INPUT_FOLDER):
        print(f"❌ Dossier d'entrée non trouvé: {INPUT_FOLDER}")
    elif not os.path.exists(MASK_FOLDER):
        print(f"❌ Dossier des masques non trouvé: {MASK_FOLDER}")
    else:
        # Créer une comparaison avant/après sur un exemple
        test_image = os.path.join(INPUT_FOLDER, "0001_0000.png")
        test_mask = os.path.join(MASK_FOLDER, "0001.png")
        
        if os.path.exists(test_image) and os.path.exists(test_mask):
            create_comparison_before_after(test_image, test_mask)
        
        # Créer tous les overlays corrigés
        create_corrected_overlays(
            INPUT_FOLDER,
            MASK_FOLDER,
            OUTPUT_FOLDER
        )
        
        print(f"\n🎯 Correction rouge/vert terminée!")
        print(f"📁 Dossier de sortie: {OUTPUT_FOLDER}")
        print(f"🔄 Maintenant: backwall=VERT, flaw=ROUGE")
        print(f"📋 Comparaison sauvegardée: red_green_correction_comparison.png")
